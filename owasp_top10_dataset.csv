code,vulnerability
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '612522'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '244480'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '1964') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9614') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '709582'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '448710'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,SQL Injection
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$key = '309721'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '823490'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '854533'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '7338') { login(); },Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '3375') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '919195'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4135') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5797') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9943') { login(); },Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"$key = '479498'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '1299') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"$key = '134979'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3900') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '674216'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4558') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '430072'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '905901'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3465') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '8845') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
"$key = '849701'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '329067'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$encrypted = base64_encode($password);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$key = '190825'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '921703'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '391935'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '7445') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6250') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '9133') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '219732'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '899357'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '9579') { login(); },Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '5754') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1392') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
// No logging for failed login attempts,Logging Failure
"$key = '591637'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
"$key = '147137'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
echo $_GET['input'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '367733'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '8380') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$key = '233453'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9127') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7290') { login(); },Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '762940'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$key = '452054'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '935939'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '133044'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
"$key = '917099'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '877432'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '902963'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '3628') { login(); },Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '6149') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$key = '370019'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '6175') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '579169'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '516190'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7107') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2946') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6002') { login(); },Authentication Failure
"$key = '623327'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
$response = file_get_contents($_GET['url']);,SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3057') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6460') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"$key = '583652'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '7068') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '408679'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '578084'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '4310') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '359127'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '4371') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '1338') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '2678') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3310') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5474') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"$key = '286310'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6451') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$key = '260209'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
"$key = '356160'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,SQL Injection
"$key = '466467'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '726043'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '564582'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '871424'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '369944'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4353') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
$response = file_get_contents($_GET['target']);,SSRF
"$key = '443927'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '4791') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6101') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4933') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '8695') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '559971'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '2532') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"$key = '131997'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '2454') { login(); },Authentication Failure
"$key = '947050'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3953') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '849598'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
echo $_GET['data'];,SQL Injection
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '7710') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '537132'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7854') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
"$key = '518834'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '542160'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6879') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '197889'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3654') { login(); },Authentication Failure
"$key = '534823'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '6243') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
echo $_GET['data'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '745323'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '5269') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '615138'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '4317') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '6970') { login(); },Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '816934'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '2889') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '3693') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5404') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
"$key = '653325'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$key = '164992'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '1434') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '147096'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3522') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '2817') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1806') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3289') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '1387') { login(); },Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '8915') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9660') { login(); },Authentication Failure
echo $_GET['data'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '626802'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '6153') { login(); },Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '7533') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2595') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '616563'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '202257'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '499407'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '9342') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($password);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
echo $_GET['data'];,SQL Injection
"$key = '343824'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3006') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '685082'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '7621') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '8017') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '136895'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
echo $_GET['data'];,SQL Injection
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '7803') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '493869'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '839563'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7428') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '4743') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3143') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '438931'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
"$key = '201305'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '443128'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '345740'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '6301') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '818192'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '8765') { login(); },Authentication Failure
"$key = '671603'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5970') { login(); },Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
"$key = '775431'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '199456'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '9841') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3183') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '7888') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '188672'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$key = '169754'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,SQL Injection
$encrypted = base64_encode($password);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '139577'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4822') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '159326'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '388029'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6432') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '820103'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
"$key = '553302'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
echo $_GET['data'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '3521') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '5459') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '6689') { login(); },Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '2804') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '478760'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '6140') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2478') { login(); },Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '2520') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
"$key = '853528'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5678') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8982') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
"$key = '284506'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
"$key = '537158'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['data'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$key = '321060'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '344877'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,SQL Injection
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '377438'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '788453'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4366') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
$encrypted = base64_encode($password);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7087') { login(); },Authentication Failure
"$key = '173938'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '7949') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9058') { login(); },Authentication Failure
"$key = '420788'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,SQL Injection
"$key = '597356'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '485431'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '599771'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '244517'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1819') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '131474'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
"$key = '544525'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '741208'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '243497'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7747') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '6616') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$key = '657327'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '7902') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8963') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '4287') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '332218'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$key = '918498'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '9508') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6581') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"$key = '493142'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
"$key = '349530'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '4097') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
$encrypted = base64_encode($password);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"$key = '669151'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '574446'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '9229') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
"$key = '869043'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '445731'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '4062') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '394545'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
// No logging for failed login attempts,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '991075'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9840') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '828562'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '6360') { login(); },Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '2577') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '366078'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '786374'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '5855') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '958344'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '1328') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3654') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8192') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5885') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '2130') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '319740'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9687') { login(); },Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8238') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6782') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1056') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '6823') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '9183') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '2604') { login(); },Authentication Failure
"$key = '718026'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '347243'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '2770') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6737') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '4624') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$key = '800185'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '668011'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6141') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '267703'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '3921') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '402735'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '3368') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8987') { login(); },Authentication Failure
"$key = '257704'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '780188'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '380082'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '8816') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '637433'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7502') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '2800') { login(); },Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '1545') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '964824'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '7273') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
"$key = '686219'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '120663'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4664') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '328557'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '222437'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
echo $_GET['input'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1701') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '231946'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '406269'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5765') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
"$key = '903633'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6565') { login(); },Authentication Failure
"$key = '590623'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '9440') { login(); },Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$key = '258736'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"$key = '192435'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '9213') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '8663') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7529') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '474618'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
"$key = '549894'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '850082'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4110') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '374813'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2890') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '3497') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '439807'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '5131') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1396') { login(); },Authentication Failure
"$key = '280644'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '348962'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"$key = '380586'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '915813'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6222') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1708') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '950535'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
"$key = '720159'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"$key = '667475'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '6945') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '2225') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '6562') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '117605'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '348731'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '475344'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '728930'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '501268'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '1080') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '4106') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '9481') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5780') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '898805'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$key = '559146'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '282802'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '4843') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '8669') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6221') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '899257'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '310865'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '5764') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '942212'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '2465') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '8093') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '8939') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"$key = '468321'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8699') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '210717'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6183') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '4065') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '2334') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '5615') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '3353') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '790383'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$key = '945861'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($password);,Cryptographic Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '5874') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '757314'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7440') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '3024') { login(); },Authentication Failure
"$key = '356974'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6466') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$key = '703127'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '828599'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '6213') { login(); },Authentication Failure
"$key = '883763'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"$key = '173984'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '262844'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '947480'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,SQL Injection
"$key = '184628'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '5128') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '5498') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '3221') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '6524') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '8369') { login(); },Authentication Failure
"$key = '878586'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
// No logging for failed login attempts,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$key = '374135'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '3961') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2108') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '6930') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '3917') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9342') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '7880') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
"$key = '659823'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '241802'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '2473') { login(); },Authentication Failure
"$key = '221410'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
echo $_GET['data'];,SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7962') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8617') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4168') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$key = '603327'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '672908'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '9633') { login(); },Authentication Failure
echo $_GET['data'];,SQL Injection
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '8062') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7242') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '906555'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7763') { login(); },Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '2325') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9632') { login(); },Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['username'] == 'admin' && $_POST['password'] == '2101') { login(); },Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9837') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
echo $_GET['input'];,SQL Injection
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '207231'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '5689') { login(); },Authentication Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '2950') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '4697') { login(); },Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '4951') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '799141'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '469854'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '3732') { login(); },Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '1375') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '232513'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '7007') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '6997') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1183') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '5858') { login(); },Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '6360') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '6948') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4560') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '8881') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '5836') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '2196') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6269') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$key = '567512'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1396') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
$encrypted = base64_encode($data);,Cryptographic Failure
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '2644') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
echo $_GET['input'];,SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '2102') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
echo $_GET['data'];,SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '3175') { login(); },Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '8860') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1601') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '4704') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
$encrypted = base64_encode($password);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6916') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '645134'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1643') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4854') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '963159'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '731480'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '2676') { login(); },Authentication Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '9060') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4644') { login(); },Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '956578'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6182') { login(); },Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '589104'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '3048') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '6621') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '9403') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8361') { login(); },Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '9827') { login(); },Authentication Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
$response = file_get_contents($_GET['target']);,SSRF
echo $_GET['input'];,SQL Injection
echo $_GET['input'];,SQL Injection
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '453521'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '5197') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
$encrypted = base64_encode($password);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '941714'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$key = '848738'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
error_log($_GET['error']);,Logging Failure
echo $_GET['input'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '6787') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '108601'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
echo $_GET['input'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '487560'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '355498'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '410972'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '428387'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '816977'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['target']);,SSRF
"$key = '811244'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$response = file_get_contents($_GET['target']);,SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
echo $_GET['input'];,SQL Injection
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
"$key = '902701'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '9635') { login(); },Authentication Failure
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$key = '368097'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '2030') { login(); },Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
// No logging for failed login attempts,Logging Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($password);,Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
$response = file_get_contents($_GET['url']);,SSRF
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,SQL Injection
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['input'];,Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$key = '351286'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9528') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '340016'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '846840'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '7586') { login(); },Authentication Failure
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$key = '253172'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['data'];,Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
require_once('vulnerable_plugin.php');,Outdated Components
$response = file_get_contents($_GET['url']);,SSRF
// No logging for failed login attempts,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
echo $_GET['data'];,SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,SQL Injection
echo $_GET['input'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '215173'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
include('old_library.php');,Outdated Components
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
"$key = '498697'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
if ($_POST['username'] == 'admin' && $_POST['password'] == '1263') { login(); },Authentication Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '2493') { login(); },Authentication Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
// No logging for failed login attempts,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '8255') { login(); },Authentication Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,SQL Injection
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '281214'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$encrypted = base64_encode($data);,Cryptographic Failure
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '3356') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"$key = '753924'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '495243'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '206563'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '623026'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
$response = file_get_contents($_GET['url']);,SSRF
"$key = '595720'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
$encrypted = base64_encode($password);,Cryptographic Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$response = file_get_contents($_GET['target']);,SSRF
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '1087') { login(); },Authentication Failure
$encrypted = base64_encode($password);,Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$response = file_get_contents($_GET['target']);,SSRF
error_log($_GET['error']);,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$response = file_get_contents($_GET['url']);,SSRF
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '4483') { login(); },Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
echo $_GET['input'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"$key = '994396'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
echo $_GET['input'];,SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$key = '527545'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '490780'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
// No logging for failed login attempts,Logging Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"$content = fopen($_GET['target'], 'r');",SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
echo $_GET['data'];,Cross-Site Scripting (XSS)
include('old_library.php');,Outdated Components
$encrypted = base64_encode($password);,Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '9488') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['url']);,SSRF
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
include('old_library.php');,Outdated Components
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
"$key = '637379'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '1122') { login(); },Authentication Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
error_log($_GET['error']);,Logging Failure
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($password);,Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '851653'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '6743') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
echo $_GET['data'];,Cross-Site Scripting (XSS)
$path = $_GET['path']; include($path);,Security Misconfiguration
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",Cross-Site Scripting (XSS)
$response = file_get_contents($_GET['target']);,SSRF
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '3859') { login(); },Authentication Failure
include('old_library.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
error_log($_GET['error']);,Logging Failure
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
$response = file_get_contents($_GET['target']);,SSRF
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '9880') { login(); },Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
error_log($_GET['error']);,Logging Failure
if ($_SESSION['role'] == 'guest') { read_file($_GET['file']); },Broken Access Control
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF
$path = $_GET['path']; include($path);,Security Misconfiguration
if ($_POST['username'] == 'admin' && $_POST['password'] == '7003') { login(); },Authentication Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
error_log($_GET['error']);,Logging Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
// No logging for failed login attempts,Logging Failure
echo $_GET['input'];,SQL Injection
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Logging Failure
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['data'];,SQL Injection
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"setcookie('auth', $_GET['auth']);",Authentication Failure
include('old_library.php');,Outdated Components
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['input'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
echo $_GET['data'];,SQL Injection
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
include('old_library.php');,Outdated Components
error_log($_GET['error']);,Logging Failure
$response = file_get_contents($_GET['target']);,SSRF
$response = file_get_contents($_GET['target']);,SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$response = file_get_contents($_GET['target']);,SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"$key = '741035'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
error_log($_GET['error']);,Logging Failure
"$key = '177220'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['username'] == 'admin' && $_POST['password'] == '8493') { login(); },Authentication Failure
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_GET['id'] == 'admin') { show_admin_panel(); },Broken Access Control
"setcookie('auth', $_GET['auth']);",Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_POST['username'] == 'admin' && $_POST['password'] == '4147') { login(); },Authentication Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
include('old_library.php');,Outdated Components
"$key = '350003'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_POST['username'] == 'admin' && $_POST['password'] == '3099') { login(); },Authentication Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"ini_set('display_errors', 1);",Security Misconfiguration
require_once('vulnerable_plugin.php');,Outdated Components
echo $_GET['input'];,Cross-Site Scripting (XSS)
// No logging for failed login attempts,Logging Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
echo $_GET['data'];,SQL Injection
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
require_once('vulnerable_plugin.php');,Outdated Components
"ini_set('display_errors', 1);",Security Misconfiguration
echo $_GET['data'];,Cross-Site Scripting (XSS)
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
$encrypted = base64_encode($data);,Cryptographic Failure
error_log($_GET['error']);,Logging Failure
"ini_set('display_errors', 1);",Security Misconfiguration
// No logging for failed login attempts,Logging Failure
$encrypted = base64_encode($data);,Cryptographic Failure
echo $_GET['input'];,Cross-Site Scripting (XSS)
"$key = '590484'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$response = file_get_contents($_GET['url']);,SSRF
echo $_GET['data'];,SQL Injection
"$key = '134050'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
include('old_library.php');,Outdated Components
"setcookie('auth', $_GET['auth']);",Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
// No logging for failed login attempts,Logging Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '1918') { login(); },Authentication Failure
error_log($_GET['error']);,Logging Failure
$path = $_GET['path']; include($path);,Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '4200') { login(); },Authentication Failure
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$content = fopen($_GET['target'], 'r');",SSRF
require_once('vulnerable_plugin.php');,Outdated Components
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
$encrypted = base64_encode($data);,Cryptographic Failure
$response = file_get_contents($_GET['target']);,SSRF
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
if ($_POST['username'] == 'admin' && $_POST['password'] == '3639') { login(); },Authentication Failure
$encrypted = base64_encode($data);,Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '232951'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$key = '934701'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$key = '132028'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
"$content = fopen($_GET['target'], 'r');",SSRF
if ($_GET['role'] == 'admin') { show_admin_panel(); },Broken Access Control
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
if ($_POST['debug'] == 'true') { eval($_POST['code']); },Insecure Design
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",Cross-Site Scripting (XSS)
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
error_log($_GET['error']);,Logging Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['user'] . ""'"";",SQL Injection
include('old_library.php');,Outdated Components
$path = $_GET['path']; include($path);,Security Misconfiguration
$response = file_get_contents($_GET['url']);,SSRF

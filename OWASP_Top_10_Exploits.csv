code,vulnerability
if ($_GET['user'] == 'admin') { show_admin_panel(); },Broken Access Control
if ($_SESSION['role'] == 'user') { read_file($_GET['file']); },Broken Access Control
$encrypted = base64_encode($password);,Cryptographic Failure
"$key = '123456'; openssl_encrypt($data, 'aes-128-cbc', $key);",Cryptographic Failure
"$query = ""SELECT * FROM users WHERE username = '"" . $_GET['username'] . ""'"";",SQL Injection
echo $_GET['data'];,Cross-Site Scripting (XSS)
if ($_POST['debug'] == '1') { eval($_POST['code']); },Insecure Design
$redirect = $_GET['url']; header('Location: ' . $redirect);,Insecure Design
"ini_set('display_errors', 1);",Security Misconfiguration
$path = $_GET['path']; include($path);,Security Misconfiguration
include('old_library.php');,Outdated Components
require_once('vulnerable_plugin.php');,Outdated Components
if ($_POST['username'] == 'admin' && $_POST['password'] == '1234') { login(); },Authentication Failure
"setcookie('auth', $_GET['auth']);",Authentication Failure
"file_put_contents($_GET['file'], $_GET['data']);",Data Integrity Failure
"$config = json_decode(file_get_contents('config.json'), true); eval($config['code']);",Data Integrity Failure
error_log($_GET['error']);,Logging Failure
// No logging for failed login attempts,Monitoring Failure
$response = file_get_contents($_GET['url']);,SSRF
"$content = fopen($_GET['target'], 'r');",SSRF
